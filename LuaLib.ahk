; LuaLib.ahk - Lua调用库
; 封装了所有Lua DLL调用的复杂操作

class LuaLib {
    ; 静态属性
    static dllPath := ""
    static hLua := 0
    static isInitialized := false

    ; Lua状态相关
    static luaState := 0
    static luaFunctions := {}
    static isPersistent := false
    
    ; 初始化Lua库 - 只需要调用一次
    static Init(luaDllPath := "", persistent := false) {
        if (this.isInitialized) {
            return true
        }

        ; 如果没有指定路径，使用默认路径
        if (luaDllPath == "") {
            luaDllPath := A_ScriptDir . "\lua54.dll"
        }

        this.dllPath := luaDllPath
        this.isPersistent := persistent

        ; 检查DLL文件是否存在
        if !FileExist(this.dllPath) {
            throw Error("找不到Lua DLL文件: " . this.dllPath)
        }

        ; 加载DLL
        this.hLua := DllCall("LoadLibrary", "Str", this.dllPath, "Ptr")
        if !this.hLua {
            throw Error("无法加载Lua DLL: " . this.dllPath)
        }

        ; 获取并缓存Lua函数地址
        this._CacheLuaFunctions()

        ; 如果启用持久化模式，创建持久的Lua状态
        if (this.isPersistent) {
            this._CreatePersistentState()
        }

        this.isInitialized := true
        return true
    }

    ; 缓存Lua函数地址（只获取一次）
    static _CacheLuaFunctions() {
        this.luaFunctions.luaL_newstate := DllCall("GetProcAddress", "Ptr", this.hLua, "AStr", "luaL_newstate", "Ptr")
        this.luaFunctions.luaL_openlibs := DllCall("GetProcAddress", "Ptr", this.hLua, "AStr", "luaL_openlibs", "Ptr")
        this.luaFunctions.luaL_loadfile := DllCall("GetProcAddress", "Ptr", this.hLua, "AStr", "luaL_loadfilex", "Ptr")
        if !this.luaFunctions.luaL_loadfile {
            this.luaFunctions.luaL_loadfile := DllCall("GetProcAddress", "Ptr", this.hLua, "AStr", "luaL_loadfile", "Ptr")
        }
        this.luaFunctions.lua_pcall := DllCall("GetProcAddress", "Ptr", this.hLua, "AStr", "lua_pcallk", "Ptr")
        if !this.luaFunctions.lua_pcall {
            this.luaFunctions.lua_pcall := DllCall("GetProcAddress", "Ptr", this.hLua, "AStr", "lua_pcall", "Ptr")
        }
        this.luaFunctions.luaL_loadstring := DllCall("GetProcAddress", "Ptr", this.hLua, "AStr", "luaL_loadstring", "Ptr")
        this.luaFunctions.lua_close := DllCall("GetProcAddress", "Ptr", this.hLua, "AStr", "lua_close", "Ptr")

        if !this.luaFunctions.luaL_newstate || !this.luaFunctions.luaL_openlibs || !this.luaFunctions.lua_close {
            throw Error("无法获取基本的Lua函数")
        }
    }

    ; 创建持久的Lua状态
    static _CreatePersistentState() {
        this.luaState := DllCall(this.luaFunctions.luaL_newstate, "Ptr")
        if !this.luaState {
            throw Error("无法创建持久的Lua状态")
        }
        DllCall(this.luaFunctions.luaL_openlibs, "Ptr", this.luaState)
    }
    
    ; 执行Lua文件
    static RunFile(luaFilePath) {
        ; 确保已初始化
        if !this.isInitialized {
            this.Init()
        }

        ; 检查Lua文件是否存在
        if !FileExist(luaFilePath) {
            throw Error("找不到Lua文件: " . luaFilePath)
        }

        if (this.isPersistent && this.luaState) {
            ; 使用持久状态执行
            return this._RunWithPersistentState(luaFilePath, true)
        } else {
            ; 使用临时状态执行
            return this._RunWithTemporaryState(luaFilePath, true)
        }
    }

    ; 使用持久状态执行（性能更好）
    static _RunWithPersistentState(luaFilePath, isFile := true) {
        if isFile {
            ; 加载并执行文件
            loadResult := DllCall(this.luaFunctions.luaL_loadfile, "Ptr", this.luaState, "AStr", luaFilePath, "Int")
            if loadResult != 0 {
                throw Error("Lua文件加载失败，错误代码: " . loadResult)
            }
        } else {
            ; 加载并执行代码字符串
            loadResult := DllCall(this.luaFunctions.luaL_loadstring, "Ptr", this.luaState, "AStr", luaFilePath, "Int")
            if loadResult != 0 {
                throw Error("Lua代码加载失败，错误代码: " . loadResult)
            }
        }

        ; 执行
        callResult := DllCall(this.luaFunctions.lua_pcall, "Ptr", this.luaState, "Int", 0, "Int", 0, "Int", 0, "Int")
        if callResult != 0 {
            throw Error("Lua执行失败，错误代码: " . callResult)
        }

        return true
    }

    ; 使用临时状态执行（兼容模式）
    static _RunWithTemporaryState(luaFilePath, isFile := true) {
        ; 创建临时Lua状态
        L := DllCall(this.luaFunctions.luaL_newstate, "Ptr")
        if !L {
            throw Error("无法创建Lua状态")
        }

        try {
            ; 打开标准库
            DllCall(this.luaFunctions.luaL_openlibs, "Ptr", L)

            if isFile {
                ; 加载并执行文件
                loadResult := DllCall(this.luaFunctions.luaL_loadfile, "Ptr", L, "AStr", luaFilePath, "Int")
                if loadResult != 0 {
                    throw Error("Lua文件加载失败，错误代码: " . loadResult)
                }
            } else {
                ; 加载并执行代码字符串
                loadResult := DllCall(this.luaFunctions.luaL_loadstring, "Ptr", L, "AStr", luaFilePath, "Int")
                if loadResult != 0 {
                    throw Error("Lua代码加载失败，错误代码: " . loadResult)
                }
            }

            ; 执行
            callResult := DllCall(this.luaFunctions.lua_pcall, "Ptr", L, "Int", 0, "Int", 0, "Int", 0, "Int")
            if callResult != 0 {
                throw Error("Lua执行失败，错误代码: " . callResult)
            }

            return true
        } finally {
            ; 确保清理临时Lua状态
            DllCall(this.luaFunctions.lua_close, "Ptr", L)
        }
    }
    
    ; 执行Lua代码字符串
    static RunCode(luaCode) {
        ; 确保已初始化
        if !this.isInitialized {
            this.Init()
        }

        if (this.isPersistent && this.luaState) {
            ; 使用持久状态执行
            return this._RunWithPersistentState(luaCode, false)
        } else {
            ; 使用临时状态执行
            return this._RunWithTemporaryState(luaCode, false)
        }
    }
    
    ; 清理资源
    static Cleanup() {
        ; 清理持久Lua状态
        if this.luaState {
            DllCall(this.luaFunctions.lua_close, "Ptr", this.luaState)
            this.luaState := 0
        }

        ; 清理DLL
        if this.hLua {
            DllCall("FreeLibrary", "Ptr", this.hLua)
            this.hLua := 0
        }

        ; 重置状态
        this.luaFunctions := {}
        this.isPersistent := false
        this.isInitialized := false
    }

    ; 启用持久模式（推荐用于频繁调用）
    static EnablePersistent() {
        if !this.isInitialized {
            this.Init("", true)
        } else if !this.isPersistent {
            this.isPersistent := true
            this._CreatePersistentState()
        }
    }

    ; 禁用持久模式
    static DisablePersistent() {
        if this.luaState {
            DllCall(this.luaFunctions.lua_close, "Ptr", this.luaState)
            this.luaState := 0
        }
        this.isPersistent := false
    }
    
    ; 检查是否已初始化
    static IsReady() {
        return this.isInitialized
    }

    ; 调用Lua函数并获取返回值
    static CallFunction(funcName, params := "") {
        if !this.isInitialized {
            this.Init()
        }

        ; 构造Lua调用代码
        luaCode := "return " . funcName . "(" . params . ")"

        ; 执行并获取结果
        return this._ExecuteAndGetResult(luaCode)
    }

    ; 获取Lua全局变量值
    static GetVariable(varName) {
        if !this.isInitialized {
            this.Init()
        }

        luaCode := "return tostring(" . varName . ")"
        return this._ExecuteAndGetResult(luaCode)
    }

    ; 设置Lua全局变量
    static SetVariable(varName, value) {
        if !this.isInitialized {
            this.Init()
        }

        ; 根据值类型构造赋值语句
        if IsNumber(value) {
            luaCode := varName . " = " . value
        } else {
            luaCode := varName . ' = "' . value . '"'
        }

        this.RunCode(luaCode)
    }

    ; 执行Lua代码并获取字符串返回值
    static _ExecuteAndGetResult(luaCode) {
        if (this.isPersistent && this.luaState) {
            return this._GetResultFromPersistentState(luaCode)
        } else {
            return this._GetResultFromTemporaryState(luaCode)
        }
    }

    ; 从持久状态获取结果
    static _GetResultFromPersistentState(luaCode) {
        ; 获取额外需要的Lua函数
        lua_tostring := DllCall("GetProcAddress", "Ptr", this.hLua, "AStr", "lua_tostring", "Ptr")
        lua_isstring := DllCall("GetProcAddress", "Ptr", this.hLua, "AStr", "lua_isstring", "Ptr")
        luaL_loadstring := this.luaFunctions.luaL_loadstring

        if !lua_tostring || !lua_isstring || !luaL_loadstring {
            throw Error("无法获取Lua字符串处理函数")
        }

        ; 加载代码
        loadResult := DllCall(luaL_loadstring, "Ptr", this.luaState, "AStr", luaCode, "Int")
        if loadResult != 0 {
            throw Error("Lua代码加载失败，错误代码: " . loadResult)
        }

        ; 执行代码
        callResult := DllCall(this.luaFunctions.lua_pcall, "Ptr", this.luaState, "Int", 0, "Int", 1, "Int", 0, "Int")
        if callResult != 0 {
            throw Error("Lua代码执行失败，错误代码: " . callResult)
        }

        ; 获取返回值
        if DllCall(lua_isstring, "Ptr", this.luaState, "Int", -1, "Int") {
            resultPtr := DllCall(lua_tostring, "Ptr", this.luaState, "Int", -1, "Ptr")
            result := StrGet(resultPtr, "UTF-8")

            ; 清理栈
            lua_pop := DllCall("GetProcAddress", "Ptr", this.hLua, "AStr", "lua_pop", "Ptr")
            if lua_pop {
                DllCall(lua_pop, "Ptr", this.luaState, "Int", 1)
            }

            return result
        } else {
            ; 清理栈
            lua_pop := DllCall("GetProcAddress", "Ptr", this.hLua, "AStr", "lua_pop", "Ptr")
            if lua_pop {
                DllCall(lua_pop, "Ptr", this.luaState, "Int", 1)
            }
            return ""
        }
    }

    ; 从临时状态获取结果（简化版本）
    static _GetResultFromTemporaryState(luaCode) {
        ; 临时实现：返回固定值，后续可以完善
        ; 完整的返回值获取需要复杂的Lua栈操作
        return "temp_result"
    }
}