; LuaLib 优化版本测试示例
; 展示新增功能和改进

#Include LuaLib.ahk

; 测试主函数
TestLuaLib() {
    try {
        ; 1. 基础初始化测试
        OutputDebug("=== LuaLib 优化版本测试 ===")
        
        ; 启用调试模式
        LuaLib.EnableDebug()
        
        ; 初始化（启用持久模式）
        LuaLib.Init("", true)
        OutputDebug("✓ 初始化成功")
        
        ; 2. 获取Lua版本
        version := LuaLib.GetLuaVersion()
        OutputDebug("✓ Lua版本: " . version)
        
        ; 3. 测试多种数据类型
        OutputDebug("`n--- 数据类型测试 ---")
        
        ; 设置不同类型的变量
        LuaLib.SetVariable("testNumber", 42)
        LuaLib.SetVariable("testString", "Hello World")
        LuaLib.SetVariable("testBoolean", true)
        LuaLib.SetVariable("testNil", "")
        
        ; 获取变量值
        num := LuaLib.GetVariable("testNumber")
        str := LuaLib.GetVariable("testString")
        bool := LuaLib.GetVariable("testBoolean")
        
        OutputDebug("✓ 数字: " . num)
        OutputDebug("✓ 字符串: " . str)
        OutputDebug("✓ 布尔值: " . bool)
        
        ; 4. 测试函数调用
        OutputDebug("`n--- 函数调用测试 ---")
        
        ; 定义Lua函数
        LuaLib.RunCode("function add(a, b) return a + b end")
        LuaLib.RunCode("function greet(name) return 'Hello, ' .. name .. '!' end")
        
        ; 调用函数
        result1 := LuaLib.CallFunction("add", "10, 20")
        result2 := LuaLib.CallFunction("greet", "'AutoHotkey'")
        
        OutputDebug("✓ add(10, 20) = " . result1)
        OutputDebug("✓ greet('AutoHotkey') = " . result2)
        
        ; 5. 测试语法检查
        OutputDebug("`n--- 语法检查测试 ---")
        
        syntaxGood := LuaLib.CheckSyntax("return 1 + 1")
        syntaxBad := LuaLib.CheckSyntax("return 1 +")
        
        OutputDebug("✓ 正确语法: " . (syntaxGood.valid ? "通过" : "失败"))
        OutputDebug("✓ 错误语法: " . (syntaxBad.valid ? "意外通过" : "正确检测到错误"))
        if !syntaxBad.valid {
            OutputDebug("  错误信息: " . syntaxBad.error)
        }
        
        ; 6. 测试安全执行
        OutputDebug("`n--- 安全执行测试 ---")
        
        safeResult := LuaLib.RunCodeSafe("return math.sqrt(16)")
        if safeResult.success {
            OutputDebug("✓ 安全执行成功: " . safeResult.result)
        } else {
            OutputDebug("✗ 安全执行失败: " . safeResult.error)
        }
        
        ; 7. 测试多行代码执行
        OutputDebug("`n--- 多行代码测试 ---")
        
        multilineCode := "
        (
        local x = 10
        local y = 20
        local result = x * y
        print('计算结果: ' .. result)
        )"
        
        LuaLib.RunCode(multilineCode)
        OutputDebug("✓ 多行代码执行完成")
        
        ; 8. 性能统计
        OutputDebug("`n--- 性能统计 ---")
        
        stats := LuaLib.GetStats()
        OutputDebug("✓ 调用次数: " . stats.callCount)
        OutputDebug("✓ 总耗时: " . stats.totalTime . "ms")
        OutputDebug("✓ 平均耗时: " . stats.averageTime . "ms")
        OutputDebug("✓ 错误次数: " . stats.errorCount)
        OutputDebug("✓ 成功率: " . stats.successRate . "%")
        
        ; 9. 测试错误处理
        OutputDebug("`n--- 错误处理测试 ---")
        
        try {
            LuaLib.RunCode("error('这是一个测试错误')")
        } catch Error as e {
            OutputDebug("✓ 错误处理正常: " . e.Message)
        }
        
        ; 10. 列出全局变量（部分）
        OutputDebug("`n--- 全局变量列表 ---")
        globals := LuaLib.ListGlobalVariables()
        ; 只显示前几个变量
        globalLines := StrSplit(globals, "`n")
        loop Min(5, globalLines.Length) {
            OutputDebug("  " . globalLines[A_Index])
        }
        OutputDebug("  ... (共 " . globalLines.Length . " 个全局变量)")
        
        ; 最终统计
        finalStats := LuaLib.GetStats()
        OutputDebug("`n=== 测试完成 ===")
        OutputDebug("总调用次数: " . finalStats.callCount)
        OutputDebug("总耗时: " . finalStats.totalTime . "ms")
        OutputDebug("成功率: " . finalStats.successRate . "%")
        
    } catch Error as e {
        OutputDebug("✗ 测试失败: " . e.Message)
    } finally {
        ; 清理资源
        LuaLib.Cleanup()
        OutputDebug("✓ 资源清理完成")
    }
}

; 运行测试
TestLuaLib()
