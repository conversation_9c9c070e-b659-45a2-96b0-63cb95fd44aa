; LuaLib 优化版本测试示例
; 展示新增功能和改进

#Include LuaLib.ahk

; 全局变量存储测试结果
global testResults := ""

; 添加测试结果的函数
AddResult(text) {
    global testResults
    testResults .= text . "`n"
}

; 测试主函数
TestLuaLib() {
    try {
        ; 1. 基础初始化测试
        AddResult("=== LuaLib 优化版本测试 ===")

        ; 启用调试模式
        LuaLib.EnableDebug()

        ; 初始化（启用持久模式）
        LuaLib.Init("", true)
        AddResult("✓ 初始化成功")

        ; 2. 获取Lua版本
        version := LuaLib.GetLuaVersion()
        AddResult("✓ Lua版本: " . version)
        
        ; 3. 测试多种数据类型
        AddResult("`n--- 数据类型测试 ---")

        ; 设置不同类型的变量
        LuaLib.SetVariable("testNumber", 42)
        LuaLib.SetVariable("testString", "Hello World")
        LuaLib.SetVariable("testBoolean", true)
        LuaLib.SetVariable("testNil", "")

        ; 获取变量值
        num := LuaLib.GetVariable("testNumber")
        str := LuaLib.GetVariable("testString")
        bool := LuaLib.GetVariable("testBoolean")

        AddResult("✓ 数字: " . num)
        AddResult("✓ 字符串: " . str)
        AddResult("✓ 布尔值: " . bool)

        ; 4. 测试函数调用
        AddResult("`n--- 函数调用测试 ---")

        ; 定义Lua函数
        LuaLib.RunCode("function add(a, b) return a + b end")
        LuaLib.RunCode("function greet(name) return 'Hello, ' .. name .. '!' end")

        ; 调用函数
        result1 := LuaLib.CallFunction("add", "10, 20")
        result2 := LuaLib.CallFunction("greet", "'AutoHotkey'")

        AddResult("✓ add(10, 20) = " . result1)
        AddResult("✓ greet('AutoHotkey') = " . result2)
        
        ; 5. 测试语法检查
        AddResult("`n--- 语法检查测试 ---")

        syntaxGood := LuaLib.CheckSyntax("return 1 + 1")
        syntaxBad := LuaLib.CheckSyntax("return 1 +")

        AddResult("✓ 正确语法: " . (syntaxGood.valid ? "通过" : "失败"))
        AddResult("✓ 错误语法: " . (syntaxBad.valid ? "意外通过" : "正确检测到错误"))
        if !syntaxBad.valid {
            AddResult("  错误信息: " . syntaxBad.error)
        }

        ; 6. 测试安全执行
        AddResult("`n--- 安全执行测试 ---")

        safeResult := LuaLib.RunCodeSafe("return math.sqrt(16)")
        if safeResult.success {
            AddResult("✓ 安全执行成功: " . safeResult.result)
        } else {
            AddResult("✗ 安全执行失败: " . safeResult.error)
        }
        
        ; 7. 测试多行代码执行
        AddResult("`n--- 多行代码测试 ---")

        multilineCode := "
        (
        local x = 10
        local y = 20
        local result = x * y
        return result
        )"

        multiResult := LuaLib.RunCode(multilineCode)
        AddResult("✓ 多行代码执行完成，结果: " . multiResult)

        ; 8. 性能统计
        AddResult("`n--- 性能统计 ---")

        stats := LuaLib.GetStats()
        AddResult("✓ 调用次数: " . stats.callCount)
        AddResult("✓ 总耗时: " . stats.totalTime . "ms")
        AddResult("✓ 平均耗时: " . stats.averageTime . "ms")
        AddResult("✓ 错误次数: " . stats.errorCount)
        AddResult("✓ 成功率: " . stats.successRate . "%")
        
        ; 9. 测试错误处理
        AddResult("`n--- 错误处理测试 ---")

        try {
            LuaLib.RunCode("error('这是一个测试错误')")
        } catch Error as e {
            AddResult("✓ 错误处理正常: " . e.Message)
        }

        ; 10. 列出全局变量（部分）
        AddResult("`n--- 全局变量列表 ---")
        globals := LuaLib.ListGlobalVariables()
        ; 只显示前几个变量
        globalLines := StrSplit(globals, "`n")
        loop Min(5, globalLines.Length) {
            AddResult("  " . globalLines[A_Index])
        }
        AddResult("  ... (共 " . globalLines.Length . " 个全局变量)")

        ; 最终统计
        finalStats := LuaLib.GetStats()
        AddResult("`n=== 测试完成 ===")
        AddResult("总调用次数: " . finalStats.callCount)
        AddResult("总耗时: " . finalStats.totalTime . "ms")
        AddResult("成功率: " . finalStats.successRate . "%")

    } catch Error as e {
        AddResult("✗ 测试失败: " . e.Message)
    } finally {
        ; 清理资源
        LuaLib.Cleanup()
        AddResult("✓ 资源清理完成")
    }
}

; 运行测试
TestLuaLib()

; 显示测试结果
ShowResults() {
    global testResults

    ; 创建GUI显示结果
    resultGui := Gui("+Resize", "LuaLib 测试结果")
    resultGui.SetFont("s10", "Consolas")

    ; 添加文本控件
    edit := resultGui.Add("Edit", "ReadOnly VScroll w600 h400", testResults)

    ; 添加按钮
    resultGui.Add("Button", "w100 h30", "复制结果").OnEvent("Click", (*) => A_Clipboard := testResults)
    resultGui.Add("Button", "x+10 w100 h30", "关闭").OnEvent("Click", (*) => resultGui.Close())

    ; 显示GUI
    resultGui.Show()
}

; 显示结果
ShowResults()
