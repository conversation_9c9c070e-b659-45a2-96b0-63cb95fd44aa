; 测试优化后的LuaLib功能
#Include LuaLib.ahk

; 简单测试
try {
    ; 初始化
    LuaLib.Init("", true)  ; 启用持久模式
    
    ; 启用调试
    LuaLib.EnableDebug()
    
    ; 测试基本功能
    result1 := LuaLib.RunCode("return 1 + 1")
    MsgBox("1 + 1 = " . result1)
    
    ; 测试变量设置和获取
    LuaLib.SetVariable("test", "Hello World")
    value := LuaLib.GetVariable("test")
    MsgBox("变量值: " . value)
    
    ; 测试函数调用
    LuaLib.RunCode("function multiply(a, b) return a * b end")
    result2 := LuaLib.CallFunction("multiply", "5, 6")
    MsgBox("5 * 6 = " . result2)
    
    ; 获取统计信息
    stats := LuaLib.GetStats()
    MsgBox("调用次数: " . stats.callCount . "`n平均耗时: " . stats.averageTime . "ms")
    
} catch Error as e {
    MsgBox("错误: " . e.Message)
} finally {
    LuaLib.Cleanup()
}
