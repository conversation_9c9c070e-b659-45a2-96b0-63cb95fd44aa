; 简单的LuaLib测试
#Include LuaLib.ahk

; 测试结果
results := ""

; 添加结果函数
AddResult(text) {
    global results
    results .= text . "`n"
}

; 主测试
try {
    AddResult("=== LuaLib 简单测试 ===")
    
    ; 检查lua54.dll是否存在
    dllPath := A_ScriptDir . "\lua54.dll"
    if !FileExist(dllPath) {
        AddResult("❌ 找不到 lua54.dll 文件")
        AddResult("请将 lua54.dll 放在脚本目录下")
        AddResult("脚本目录: " . A_ScriptDir)
    } else {
        AddResult("✓ 找到 lua54.dll 文件")
        
        ; 尝试初始化
        try {
            LuaLib.Init("", true)
            AddResult("✓ LuaLib 初始化成功")
            
            ; 简单测试
            result := LuaLib.RunCode("return 'Hello from Lua!'")
            AddResult("✓ 基本执行测试: " . result)
            
            ; 数学测试
            mathResult := LuaLib.RunCode("return 2 + 3")
            AddResult("✓ 数学计算: 2 + 3 = " . mathResult)
            
            ; 变量测试
            LuaLib.SetVariable("myVar", "测试变量")
            varResult := LuaLib.GetVariable("myVar")
            AddResult("✓ 变量操作: " . varResult)
            
            ; 获取统计
            stats := LuaLib.GetStats()
            AddResult("✓ 调用统计: " . stats.callCount . " 次调用")
            
        } catch Error as e {
            AddResult("❌ LuaLib 测试失败: " . e.Message)
        } finally {
            LuaLib.Cleanup()
            AddResult("✓ 资源清理完成")
        }
    }
    
} catch Error as e {
    AddResult("❌ 测试异常: " . e.Message)
}

; 显示结果
MsgBox(results, "LuaLib 测试结果", "T10")
