# 游戏宏系统设计文档

## 📋 项目概述

基于 AutoHotkey v2.0 + Lua 的智能游戏宏系统，实现自动技能释放、智能连招、刷新循环等高级功能。

### 核心理念
- **AHK负责系统交互**：进程绑定、取色、点击、热键、GUI、托盘
- **Lua负责智能决策**：技能分析、优先级判断、连招逻辑、智能算法
- **双向数据交互**：AHK ↔ Lua 实时数据传递和指令执行

## 🏗️ 系统架构

### 整体分层设计
```
┌─────────────────────────────────────┐
│           游戏进程层                 │
│    (游戏窗口、技能图标、颜色)         │
└─────────────────┬───────────────────┘
                  │
┌─────────────────▼───────────────────┐
│           AHK交互层                 │
│  进程绑定│取色│点击│热键│GUI│托盘     │
└─────────────────┬───────────────────┘
                  │ 数据传递
┌─────────────────▼───────────────────┐
│           Lua决策层                 │
│  技能分析│优先级│连招│刷新│智能判断   │
└─────────────────┬───────────────────┘
                  │ 返回指令
┌─────────────────▼───────────────────┐
│           执行反馈层                 │
│      AHK执行Lua返回的操作指令        │
└─────────────────────────────────────┘
```

### 核心模块
#### AHK端模块
- **ProcessManager**: 进程绑定与窗口管理
- **ColorDetector**: 技能坐标取色检测
- **InputController**: 键盘鼠标输入控制
- **LuaInterface**: Lua引擎交互接口
- **GUIManager**: 用户界面管理
- **SkillExecutor**: 技能执行控制器

#### Lua端模块
- **SkillAnalyzer**: 技能状态分析
- **DecisionEngine**: 智能决策引擎
- **ComboSystem**: 连招系统
- **RefreshCycleManager**: 刷新循环管理
- **RepeatPressManager**: 重复按键管理
- **ConfigManager**: 配置管理

## 🎮 GUI界面设计

### 主控制面板
```
┌─────────────────────────────────────┐
│           游戏宏控制面板             │
├─────────────────────────────────────┤
│ 游戏进程: [nshm.exe] [已连接✓]      │
│ 窗口分辨率: [1920x1080]             │
├─────────────────────────────────────┤
│ 宏状态: [●停止中] [●运行中]          │
│ Lua引擎: [●已加载] [●未加载]        │
├─────────────────────────────────────┤
│ [启动宏] [停止宏] [重新绑定进程]     │
│ [设置]   [日志]   [退出程序]        │
└─────────────────────────────────────┘
```

### 设置界面
```
┌─────────────────────────────────────┐
│             宏设置                  │
├─────────────────────────────────────┤
│ 检测间隔: [200]ms                   │
│ 颜色阈值: [50000]                   │
├─────────────────────────────────────┤
│ 技能坐标设置:                        │
│ 技能1: X[100] Y[500] [取色] [测试]   │
│ 技能2: X[150] Y[500] [取色] [测试]   │
│ 技能3: X[200] Y[500] [取色] [测试]   │
│ 技能4: X[250] Y[500] [取色] [测试]   │
│ 技能5: X[300] Y[500] [取色] [测试]   │
│ 技能Q: X[350] Y[500] [取色] [测试]   │
│ 技能R: X[400] Y[500] [取色] [测试]   │
│ 技能T: X[450] Y[500] [取色] [测试]   │
│ 技能G: X[500] Y[500] [取色] [测试]   │
│ 技能E: X[550] Y[500] [取色] [测试]   │
├─────────────────────────────────────┤
│ [保存设置] [恢复默认] [取色所有技能]  │
└─────────────────────────────────────┘
```

### 颜色配置界面
```
┌─────────────────────────────────────┐
│           技能颜色配置               │
├─────────────────────────────────────┤
│ 技能1 可用颜色: [0x00FF00] [重新取色]│
│ 技能1 冷却颜色: [0x808080] [重新取色]│
│ 技能1 禁用颜色: [0xFF0000] [重新取色]│
├─────────────────────────────────────┤
│ 技能2 可用颜色: [0x00FF00] [重新取色]│
│ 技能2 冷却颜色: [0x808080] [重新取色]│
│ 技能2 禁用颜色: [0xFF0000] [重新取色]│
├─────────────────────────────────────┤
│ ... (其他8个技能的颜色配置)          │
├─────────────────────────────────────┤
│ [保存颜色] [批量取色] [恢复默认]      │
└─────────────────────────────────────┘
```

## ⚙️ 配置系统

### Lua配置文件 (config.lua)
```lua
-- 游戏宏配置文件
Config = {
    -- 基础设置
    settings = {
        detectionInterval = 200,    -- 检测间隔(毫秒)
        colorThreshold = 50000,     -- 颜色阈值
        gameProcessName = "game.exe" -- 游戏进程名
    },

    -- 技能坐标配置
    skills = {
        ["1"] = {
            x = 100, y = 500,
            defaultKey = "1", userKey = "1",
            type = "normal",
            independent = false,    -- 是否独立技能
            repeatPress = false,    -- 是否需要重复按键
            priority = 1,
            -- 个人化颜色配置 (AHK取色后保存)
            colors = {
                ready = 0x00FF00,      -- 该用户的技能可用颜色
                cooldown = 0x808080,   -- 该用户的技能冷却颜色
                disabled = 0xFF0000    -- 该用户的技能禁用颜色
            }
        },
        ["e"] = {
            x = 550, y = 500,
            defaultKey = "e", userKey = "4",  -- 用户自定义按键
            type = "default",       -- 默认普攻技能
            independent = false,
            repeatPress = true,     -- 需要重复按键
            repeatSettings = {
                interval = 50,
                maxDuration = 2000,
                successCheck = true
            },
            priority = 99,
            -- 个人化颜色配置
            colors = {
                ready = 0x00AA00,      -- 可能与技能1颜色略有不同
                cooldown = 0x707070,
                disabled = 0xAA0000
            }
        }
    },

    -- 全局默认颜色 (首次使用的默认值)
    defaultColors = {
        skillReady = 0x00FF00,     -- 技能可用(绿色)
        skillCooldown = 0x808080,  -- 技能冷却(灰色)
        skillDisabled = 0xFF0000   -- 技能禁用(红色)
    }
}
```

## 🔄 工作流程

### 1. 初始化流程
```
1.1 AHK启动 → 创建GUI主界面
1.2 自动检测游戏进程 → 扫描预设的游戏进程名
1.3 绑定游戏窗口 → 获取窗口句柄和分辨率
1.4 在GUI上显示窗口信息 → 实时显示分辨率
1.5 初始化LuaLib → 启用持久模式
1.6 加载Lua决策引擎 → 技能分析算法
1.7 GUI显示"准备就绪" → 等待用户按F1启动
1.8 用户按F1 → 启动主循环定时器(每200ms)
```

### 2. 主循环流程 (每200ms)
```
2.1 检查游戏窗口是否激活
    ├─ 不激活 → 跳过本次循环
    └─ 激活 → 继续执行

2.2 AHK取色阶段
    ├─ 获取游戏窗口坐标
    ├─ 遍历10个技能位置
    ├─ 取每个技能图标颜色
    └─ 构造技能数据包

2.3 数据传递给Lua
    ├─ 转换为Lua数据格式
    ├─ 调用Lua决策函数
    └─ 等待Lua返回结果

2.4 Lua智能分析
    ├─ 颜色分析 → 判断技能状态(可用/冷却/禁用)
    ├─ 优先级判断 → 治疗>连招>攻击>辅助>普攻
    ├─ 连招逻辑 → 序列连招、刷新循环
    ├─ 重复按键 → 处理难释放的技能
    └─ 返回执行指令

2.5 AHK执行指令
    ├─ 解析Lua返回值
    ├─ 执行对应操作(按键/重复按键)
    └─ 更新GUI状态显示
```

## 🎯 技能系统

### 技能类型分类
1. **普通技能**: 按一次即可释放
2. **重复按键技能**: 需要重复按键直到成功释放
3. **独立技能**: 可以与其他技能同时释放
4. **默认普攻**: 无其他技能时持续释放
5. **连招技能**: 参与连招组合的技能

### 技能优先级系统
```
优先级顺序 (数字越小优先级越高):
1. 治疗技能 (priority = 1)
2. 连招技能 (priority = 2)
3. 普通攻击技能 (priority = 3-10)
4. 辅助技能 (priority = 11-20)
5. 默认普攻 (priority = 99)
```

### 自定义按键支持
- 每个技能支持用户自定义按键映射
- 保持取色坐标不变，只改变发送的按键
- 示例：E技能坐标不变，但发送4键

## 🔗 连招系统

### 1. 序列连招 (Q+3类型)
```lua
{
    name = "Q+3连招",
    type = "sequence",
    skills = {
        {skill = "q", wait = 500},  -- Q技能后等待500ms
        {skill = "3", wait = 0}     -- 3技能无等待
    },
    priority = 1,
    condition = "both_ready"
}
```

**特点**:
- 按固定顺序执行
- 支持技能间等待时间
- 支持重复按键技能
- 任一技能失败则重试或终止

### 2. 刷新循环连招 (2+4类型)
```lua
{
    name = "2+4刷新连招",
    type = "refresh_cycle",
    skills = {
        {
            skill = "2",
            refreshTarget = "4",        -- 2技能会刷新4技能
            repeatInterval = 40,
            maxRepeatTime = 1500,
            nextDelay = 100
        },
        {
            skill = "4", 
            refreshTarget = "2",        -- 4技能会刷新2技能
            repeatInterval = 40,
            maxRepeatTime = 1500,
            nextDelay = 100
        }
    },
    priority = 2,
    condition = "any_ready",
    autoEnd = true,                 -- 自动结束
    noRefreshTimeout = 2000         -- 无刷新超时时间
}
```

**工作原理**:
1. 重复按2技能 → 4技能从不可用变可用 → 2技能成功
2. 切换到4技能 → 重复按4技能 → 2技能刷新可用 → 4技能成功
3. 循环执行：2→4→2→4→2→4...
4. 当某次释放后目标技能不再刷新时，连招自动结束

**智能结束机制**:
- 不需要预设循环次数
- 根据游戏实际刷新机制自动判断结束
- 适应不同装备、版本的变化

## 🔄 重复按键系统

### 设计理念
某些技能比较"卡手"，需要重复按键直到成功释放为止。

### 工作机制
```
1. 开始重复按键 (每X毫秒按一次)
2. 同时监控技能颜色变化
3. 颜色从"可用"变为"冷却" → 技能成功释放
4. 立即停止重复按键
5. 或超时后强制停止
```

### 配置示例
```lua
repeatSettings = {
    interval = 40,          -- 重复间隔40ms
    maxDuration = 1500,     -- 最大重复1.5秒
    successCheck = true,    -- 启用成功检测
    stopOnSuccess = true    -- 成功后立即停止
}
```

## 🔧 独立技能系统

### 概念
独立技能可以与其他技能同时释放，不受连招限制。

### 应用场景
- R技能(辅助技能)在Q+3连招期间也可以释放
- 支持同时按下多个按键
- 不会打断当前连招序列

### 执行方式
```
方法1: 快速连续发送 (模拟同时按下)
- 同时按下所有独立技能键
- 保持10ms后同时释放

方法2: 组合键发送 (如果游戏支持)
- 发送组合键如 "r+3"
```

## 🎨 颜色取色系统

### 取色工作流程
```
1. 用户在设置界面点击"取色"按钮
2. AHK根据技能坐标获取当前颜色
3. 提示用户确保技能处于指定状态(可用/冷却/禁用)
4. 保存颜色值到Lua配置文件
5. 更新内存中的配置
6. 显示取色成功提示
```

### 批量取色功能
```
1. 点击"批量取色"按钮
2. 程序提示用户操作步骤：
   - "请确保所有技能都处于可用状态，然后按确定"
   - 取色所有技能的可用颜色
   - "请等待技能进入冷却状态，然后按确定"
   - 取色所有技能的冷却颜色
   - "请在技能禁用时按确定"
   - 取色所有技能的禁用颜色
3. 自动保存所有颜色配置
```

### 颜色验证机制
```
取色后自动验证：
1. 检查颜色值是否合理 (不为纯黑或纯白)
2. 检查与默认颜色的差异度
3. 提供颜色预览功能
4. 支持手动修正颜色值
```

### 个人化颜色存储
每个用户的颜色配置独立存储，因为：
- 不同显卡/显示器的颜色可能略有差异
- 游戏设置(亮度、对比度)影响颜色
- 不同分辨率下颜色可能有细微变化
- 游戏皮肤/主题影响技能图标颜色

## 📊 数据交互协议

### AHK → Lua 数据格式
```lua
skillData = {
    skill1 = {color = 0x00FF00, x = 100, y = 500},
    skill2 = {color = 0x808080, x = 150, y = 500},
    skillq = {color = 0xFF0000, x = 350, y = 500},
    -- ... 其他技能
}
```

### Lua → AHK 指令格式
```
返回值类型：
- "cast_skill1" → 释放普通技能1
- "repeat_press_skill2" → 重复按键技能2
- "refresh_repeat_skill4" → 刷新连招中重复按键
- "cast_independent_r" → 释放独立技能R
- "wait" → 等待
```

### AHK取色实现
```ahk
; 颜色配置管理器
class ColorConfigManager {
    ; 单个技能取色
    static CaptureSkillColor(skillKey, colorType) {
        ; 获取技能坐标
        skillConfig := this.GetSkillConfig(skillKey)
        if (!skillConfig) {
            MsgBox("未找到技能配置: " . skillKey)
            return false
        }

        ; 获取游戏窗口位置
        WinGetPos(&winX, &winY, , , "ahk_id " . ProcessManager.gameHwnd)

        ; 计算绝对坐标
        absX := winX + skillConfig.x
        absY := winY + skillConfig.y

        ; 获取颜色
        color := PixelGetColor(absX, absY)

        ; 保存到Lua配置
        this.SaveColorToLua(skillKey, colorType, color)

        ; 显示确认信息
        colorHex := Format("0x{:06X}", color)
        MsgBox("技能" . skillKey . " " . colorType . "颜色已保存: " . colorHex)

        return true
    }

    ; 批量取色
    static BatchCaptureColors() {
        ; 取可用状态颜色
        result := MsgBox("请确保所有技能都处于可用状态，然后点击确定开始取色", "批量取色", "OKCancel")
        if (result == "Cancel") {
            return
        }

        this.CaptureAllSkillsColor("ready")

        ; 取冷却状态颜色
        result := MsgBox("请等待技能进入冷却状态，然后点击确定", "批量取色", "OKCancel")
        if (result == "Cancel") {
            return
        }

        this.CaptureAllSkillsColor("cooldown")

        ; 取禁用状态颜色
        result := MsgBox("请在技能禁用时点击确定", "批量取色", "OKCancel")
        if (result == "Cancel") {
            return
        }

        this.CaptureAllSkillsColor("disabled")

        MsgBox("批量取色完成！所有颜色配置已保存。")
    }

    ; 取所有技能的指定状态颜色
    static CaptureAllSkillsColor(colorType) {
        skillKeys := ["1", "2", "3", "4", "5", "q", "r", "t", "g", "e"]

        for skillKey in skillKeys {
            this.CaptureSkillColor(skillKey, colorType)
            Sleep(100)  ; 短暂延迟
        }
    }

    ; 保存颜色到Lua配置
    static SaveColorToLua(skillKey, colorType, color) {
        ; 构造Lua代码来更新配置
        luaCode := "
        if not Config.skills['" . skillKey . "'].colors then
            Config.skills['" . skillKey . "'].colors = {}
        end
        Config.skills['" . skillKey . "'].colors." . colorType . " = " . color . "
        "

        ; 执行Lua代码
        LuaLib.RunCode(luaCode)

        ; 保存到文件
        LuaLib.RunCode("saveConfig()")
    }

    ; 获取技能配置
    static GetSkillConfig(skillKey) {
        ; 从Lua获取技能配置
        luaCode := "return Config.skills['" . skillKey . "']"
        return LuaLib.CallFunction("", luaCode)
    }
}
```

### 颜色分析算法
```lua
-- Lua中的颜色分析函数
function analyzeSkillState(skillKey, currentColor)
    local skillConfig = Config.skills[skillKey]
    local colors = skillConfig.colors
    local threshold = Config.settings.colorThreshold

    -- 计算与各状态颜色的相似度
    local readySimilarity = colorSimilarity(currentColor, colors.ready)
    local cooldownSimilarity = colorSimilarity(currentColor, colors.cooldown)
    local disabledSimilarity = colorSimilarity(currentColor, colors.disabled)

    -- 选择最相似的状态
    if readySimilarity < threshold then
        return "ready"
    elseif cooldownSimilarity < threshold then
        return "cooldown"
    elseif disabledSimilarity < threshold then
        return "disabled"
    else
        return "unknown"
    end
end

-- 颜色相似度计算
function colorSimilarity(color1, color2)
    local r1, g1, b1 = extractRGB(color1)
    local r2, g2, b2 = extractRGB(color2)

    local dr = r1 - r2
    local dg = g1 - g2
    local db = b1 - b2

    return math.sqrt(dr*dr + dg*dg + db*db)
end

-- RGB提取函数
function extractRGB(color)
    local r = math.floor(color / 65536) % 256
    local g = math.floor(color / 256) % 256
    local b = color % 256
    return r, g, b
end
```

## 🛡️ 安全与控制

### 进程绑定安全
- 严格窗口检测，只在目标游戏内工作
- 热键作用域限制，游戏外热键无效
- 自动暂停机制，游戏失焦自动停止

### 异常处理
- 错误捕获，防止程序崩溃
- 超时保护，避免无限循环
- 自动恢复，轻微错误自动重试
- 安全退出，确保资源正确释放

### 性能优化
- Lua持久化模式，避免重复创建状态
- 函数地址缓存，避免重复获取
- 智能循环间隔，根据游戏状态调整
- 内存管理，及时清理无用资源

## 🚀 扩展性设计

### 配置热更新
- 用户修改配置 → 立即更新Lua内存 → 实时生效
- 可选保存到文件，支持配置持久化

### 模块化架构
- 各功能模块独立，便于维护和扩展
- 新增技能类型只需修改Lua配置
- 支持插件式功能扩展

### 多游戏支持
- 通过配置文件适配不同游戏
- 进程名、窗口标题、技能坐标可配置
- 决策算法可针对游戏特点定制

## 📝 使用说明

### 快速开始
1. 启动游戏
2. 运行宏程序
3. 程序自动检测并绑定游戏进程
4. 在设置中配置技能坐标
5. 按F1启动宏，按F1停止宏
6. 按F2退出程序

### 配置技能坐标
1. 点击"设置"按钮
2. 修改技能坐标和按键映射
3. 调整检测间隔和颜色阈值
4. 保存设置

### 配置技能颜色
1. 确保游戏中技能处于可用状态
2. 点击对应技能的"取色"按钮
3. 程序自动获取当前坐标的颜色
4. 重复以上步骤获取冷却和禁用状态的颜色
5. 或使用"批量取色"功能一次性配置所有颜色

### 颜色配置建议
- **首次使用**：建议使用批量取色功能
- **颜色异常**：可以手动调整颜色阈值
- **定期校准**：游戏更新后重新取色
- **备份配置**：保存config.lua文件作为备份

### 自定义连招
1. 编辑config.lua文件
2. 在combos部分添加新连招
3. 配置技能序列和参数
4. 重启程序生效

---

*本文档描述了完整的游戏宏系统设计方案，涵盖了架构设计、功能实现、配置管理等各个方面。系统采用AHK+Lua双引擎架构，实现了智能化的游戏辅助功能。*
